import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAuth } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyC27wwiggr2c3OEAIyVBCJD4odZWsSw9DE",
  authDomain: "diy-project-planner-4b953.firebaseapp.com",
  projectId: "diy-project-planner-4b953",
  storageBucket: "diy-project-planner-4b953.firebasestorage.app",
  messagingSenderId: "1058449294784",
  appId: "1:1058449294784:web:6b89d4ecaaf867e73bb425",
  measurementId: "G-RV616CEX39"
};

const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const auth = getAuth(app);